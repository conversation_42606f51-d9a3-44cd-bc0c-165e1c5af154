import mongoose, { Document, Schema } from "mongoose";
import { User } from "./user.model";

export interface IPlaylist extends Document {
	name: string;
	description?: string;
	imageUrl?: string;
	userId: mongoose.Types.ObjectId;
	songs: mongoose.Types.ObjectId[];
	isPublic: boolean;
	collaborators?: mongoose.Types.ObjectId[];
	createdAt: Date;
	updatedAt: Date;
}

const playlistSchema = new Schema<IPlaylist>(
	{
		name: {
			type: String,
			required: true,
			trim: true,
		},
		description: {
			type: String,
			trim: true,
		},
		imageUrl: {
			type: String,
			default: null,
		},
		userId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		songs: [
			{
				type: mongoose.Schema.Types.ObjectId,
				ref: "Song",
			},
		],
		isPublic: {
			type: Boolean,
			default: true,
		},
		collaborators: [
			{
				type: mongoose.Schema.Types.ObjectId,
				ref: "User",
			},
		],
	},
	{ timestamps: true }
);

// Pre-save hook to validate userId exists
playlistSchema.pre('save', async function(next) {
	// Only validate userId if it's being modified or is new
	if (this.isModified('userId') || this.isNew) {
		try {
			const userExists = await User.findById(this.userId);
			if (!userExists) {
				const error = new Error(`User with ID ${this.userId} does not exist`);
				error.name = 'ValidationError';
				return next(error);
			}
		} catch (error) {
			return next(error);
		}
	}
	next();
});

// Index for efficient querying
playlistSchema.index({ userId: 1, createdAt: -1 });
playlistSchema.index({ isPublic: 1 });
playlistSchema.index({ name: "text", description: "text" });

export const Playlist = mongoose.model<IPlaylist>("Playlist", playlistSchema);
