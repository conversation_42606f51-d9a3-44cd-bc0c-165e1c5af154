import mongoose from "mongoose";
import { Playlist } from "../models/playlist.model";
import { User } from "../models/user.model";
import { config } from "dotenv";
config();
const findOrphanedPlaylists = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log("Connected to MongoDB");
        // Find all playlists with null userId
        const orphanedPlaylists = await Playlist.find({
            $or: [
                { userId: null },
                { userId: { $exists: false } }
            ]
        }).select("name description isPublic userId songs createdAt updatedAt");
        console.log(`\nFound ${orphanedPlaylists.length} orphaned playlists:`);
        console.log("=".repeat(80));
        // Also check for playlists with invalid ObjectId references
        const playlistsWithInvalidUsers = await Playlist.aggregate([
            {
                $lookup: {
                    from: "user",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userInfo"
                }
            },
            {
                $match: {
                    $and: [
                        { userId: { $ne: null } },
                        { userId: { $exists: true } },
                        { userInfo: { $size: 0 } }
                    ]
                }
            },
            {
                $project: {
                    name: 1,
                    description: 1,
                    isPublic: 1,
                    userId: 1,
                    songs: 1,
                    createdAt: 1,
                    updatedAt: 1
                }
            }
        ]);
        console.log(`\nFound ${playlistsWithInvalidUsers.length} playlists with invalid user references:`);
        console.log("=".repeat(80));
        // Display details of orphaned playlists
        if (orphanedPlaylists.length > 0) {
            for (const playlist of orphanedPlaylists) {
                console.log(`
ID: ${playlist._id}
Name: "${playlist.name}"
Description: "${playlist.description || 'N/A'}"
Is Public: ${playlist.isPublic}
User ID: ${playlist.userId}
Songs Count: ${playlist.songs.length}
Created: ${playlist.createdAt}
Updated: ${playlist.updatedAt}
${"─".repeat(60)}`);
            }
        }
        else {
            console.log("No null userId playlists found!");
        }
        // Display playlists with invalid user references
        if (playlistsWithInvalidUsers.length > 0) {
            for (const playlist of playlistsWithInvalidUsers) {
                console.log(`
ID: ${playlist._id}
Name: "${playlist.name}"
Description: "${playlist.description || 'N/A'}"
Is Public: ${playlist.isPublic}
User ID: ${playlist.userId} (INVALID - user doesn't exist)
Songs Count: ${playlist.songs.length}
Created: ${playlist.createdAt}
Updated: ${playlist.updatedAt}
${"─".repeat(60)}`);
            }
        }
        else {
            console.log("No playlists with invalid user references found!");
        }
        // Get total count of all playlists for context
        const totalPlaylists = await Playlist.countDocuments();
        const totalProblematicPlaylists = orphanedPlaylists.length + playlistsWithInvalidUsers.length;
        console.log(`\nSummary:`);
        console.log(`Total playlists: ${totalPlaylists}`);
        console.log(`Null userId playlists: ${orphanedPlaylists.length}`);
        console.log(`Invalid user reference playlists: ${playlistsWithInvalidUsers.length}`);
        console.log(`Total problematic playlists: ${totalProblematicPlaylists}`);
        if (totalPlaylists > 0) {
            console.log(`Percentage problematic: ${((totalProblematicPlaylists / totalPlaylists) * 100).toFixed(2)}%`);
        }
        // Check if there are any users in the database to potentially assign orphaned playlists to
        const userCount = await User.countDocuments();
        console.log(`Total users in database: ${userCount}`);
        if (userCount > 0) {
            const firstUser = await User.findOne().select("_id fullName email");
            console.log(`\nFirst user found: ${firstUser?.fullName} (${firstUser?._id})`);
        }
    }
    catch (error) {
        console.error("Error finding orphaned playlists:", error);
    }
    finally {
        mongoose.connection.close();
    }
};
const deleteOrphanedPlaylists = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log("Connected to MongoDB");
        // Find orphaned playlists (null userId)
        const orphanedPlaylists = await Playlist.find({
            $or: [
                { userId: null },
                { userId: { $exists: false } }
            ]
        });
        // Find playlists with invalid user references
        const playlistsWithInvalidUsers = await Playlist.aggregate([
            {
                $lookup: {
                    from: "user",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userInfo"
                }
            },
            {
                $match: {
                    $and: [
                        { userId: { $ne: null } },
                        { userId: { $exists: true } },
                        { userInfo: { $size: 0 } }
                    ]
                }
            }
        ]);
        const totalProblematic = orphanedPlaylists.length + playlistsWithInvalidUsers.length;
        if (totalProblematic === 0) {
            console.log("No problematic playlists found to delete!");
            return;
        }
        console.log(`Found ${totalProblematic} problematic playlists to delete:`);
        // Show orphaned playlists
        if (orphanedPlaylists.length > 0) {
            console.log(`\nNull userId playlists (${orphanedPlaylists.length}):`);
            for (const playlist of orphanedPlaylists) {
                console.log(`- "${playlist.name}" (${playlist._id})`);
            }
        }
        // Show playlists with invalid user references
        if (playlistsWithInvalidUsers.length > 0) {
            console.log(`\nInvalid user reference playlists (${playlistsWithInvalidUsers.length}):`);
            for (const playlist of playlistsWithInvalidUsers) {
                console.log(`- "${playlist.name}" (${playlist._id}) - userId: ${playlist.userId}`);
            }
        }
        // Delete orphaned playlists (null userId)
        const orphanedResult = await Playlist.deleteMany({
            $or: [
                { userId: null },
                { userId: { $exists: false } }
            ]
        });
        // Delete playlists with invalid user references
        const invalidUserIds = playlistsWithInvalidUsers.map(p => p._id);
        const invalidResult = await Playlist.deleteMany({
            _id: { $in: invalidUserIds }
        });
        const totalDeleted = orphanedResult.deletedCount + invalidResult.deletedCount;
        console.log(`\nDeleted ${totalDeleted} problematic playlists successfully!`);
        console.log(`- Null userId playlists: ${orphanedResult.deletedCount}`);
        console.log(`- Invalid user reference playlists: ${invalidResult.deletedCount}`);
    }
    catch (error) {
        console.error("Error deleting problematic playlists:", error);
    }
    finally {
        mongoose.connection.close();
    }
};
const assignOrphanedPlaylistsToUser = async (targetUserId) => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log("Connected to MongoDB");
        // Verify the target user exists
        const targetUser = await User.findById(targetUserId);
        if (!targetUser) {
            console.error(`User with ID ${targetUserId} not found!`);
            return;
        }
        console.log(`Target user: ${targetUser.fullName} (${targetUser._id})`);
        // Find orphaned playlists
        const orphanedPlaylists = await Playlist.find({
            $or: [
                { userId: null },
                { userId: { $exists: false } }
            ]
        });
        if (orphanedPlaylists.length === 0) {
            console.log("No orphaned playlists found to assign!");
            return;
        }
        console.log(`Found ${orphanedPlaylists.length} orphaned playlists to assign:`);
        for (const playlist of orphanedPlaylists) {
            console.log(`- "${playlist.name}" (${playlist._id})`);
        }
        // Update orphaned playlists to assign them to the target user
        const result = await Playlist.updateMany({
            $or: [
                { userId: null },
                { userId: { $exists: false } }
            ]
        }, {
            $set: { userId: new mongoose.Types.ObjectId(targetUserId) }
        });
        console.log(`\nAssigned ${result.modifiedCount} orphaned playlists to user ${targetUser.fullName}!`);
    }
    catch (error) {
        console.error("Error assigning orphaned playlists:", error);
    }
    finally {
        mongoose.connection.close();
    }
};
// Main execution
const main = async () => {
    const action = process.argv[2];
    const userId = process.argv[3];
    switch (action) {
        case "find":
            await findOrphanedPlaylists();
            break;
        case "delete":
            console.log("WARNING: This will permanently delete all orphaned playlists!");
            console.log("Press Ctrl+C to cancel, or wait 5 seconds to continue...");
            await new Promise(resolve => setTimeout(resolve, 5000));
            await deleteOrphanedPlaylists();
            break;
        case "assign":
            if (!userId) {
                console.error("Please provide a user ID to assign orphaned playlists to.");
                console.log("Usage: npm run fix-playlists assign <userId>");
                process.exit(1);
            }
            await assignOrphanedPlaylistsToUser(userId);
            break;
        default:
            console.log("Usage:");
            console.log("  npm run fix-playlists find                    # Find orphaned playlists");
            console.log("  npm run fix-playlists delete                 # Delete orphaned playlists");
            console.log("  npm run fix-playlists assign <userId>        # Assign orphaned playlists to user");
            break;
    }
};
main().catch(console.error);
//# sourceMappingURL=fix-orphaned-playlists.js.map