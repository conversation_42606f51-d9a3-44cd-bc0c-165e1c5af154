import mongoose from "mongoose";
import dotenv from "dotenv";
// Load environment variables
dotenv.config();
const cleanupCollections = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log("Connected to MongoDB");
        // Get database instance
        const db = mongoose.connection.db;
        // Check if 'users' collection exists and is empty
        const collections = await db.listCollections({ name: 'users' }).toArray();
        if (collections.length === 0) {
            console.log("✅ 'users' collection does not exist - nothing to clean up");
            return;
        }
        const usersCount = await db.collection('users').countDocuments();
        console.log(`📊 'users' collection found with ${usersCount} documents`);
        if (usersCount > 0) {
            console.log("⚠️  WARNING: 'users' collection is not empty!");
            console.log("   This script will only drop empty collections for safety.");
            console.log("   Please investigate the data before proceeding manually.");
            // Show sample documents
            const samples = await db.collection('users').find({}).limit(3).toArray();
            console.log("   Sample documents:");
            for (let i = 0; i < samples.length; i++) {
                console.log(`   ${i + 1}. ${JSON.stringify(samples[i], null, 2)}`);
            }
            return;
        }
        // Confirm the 'user' collection (singular) exists and has data
        const userCollections = await db.listCollections({ name: 'user' }).toArray();
        if (userCollections.length === 0) {
            console.log("⚠️  WARNING: 'user' collection (singular) does not exist!");
            console.log("   This is unexpected. Please verify your database setup.");
            return;
        }
        const userCount = await db.collection('user').countDocuments();
        console.log(`✅ 'user' collection (singular) found with ${userCount} documents`);
        if (userCount === 0) {
            console.log("⚠️  WARNING: 'user' collection is empty!");
            console.log("   This might indicate a problem with user synchronization.");
            console.log("   Please verify users are being created properly before dropping 'users' collection.");
            return;
        }
        // Safe to drop the empty 'users' collection
        console.log("\n🗑️  Dropping empty 'users' collection...");
        await db.collection('users').drop();
        console.log("✅ Successfully dropped empty 'users' collection");
        // Verify the cleanup
        const remainingCollections = await db.listCollections().toArray();
        const userRelatedCollections = remainingCollections.filter(c => c.name.toLowerCase().includes('user'));
        console.log("\n📋 Remaining user-related collections:");
        for (const collection of userRelatedCollections) {
            const count = await db.collection(collection.name).countDocuments();
            console.log(`   - ${collection.name}: ${count} documents`);
        }
        console.log("\n✅ Database cleanup completed successfully!");
        console.log("   - Dropped empty 'users' collection (plural)");
        console.log("   - Kept 'user' collection (singular) with user data");
        console.log("   - All user references now point to the correct collection");
    }
    catch (error) {
        if (error.message?.includes('ns not found')) {
            console.log("✅ 'users' collection already doesn't exist - cleanup not needed");
        }
        else {
            console.error("❌ Error during cleanup:", error);
        }
    }
    finally {
        mongoose.connection.close();
    }
};
// Run the cleanup
cleanupCollections();
//# sourceMappingURL=cleanup-collections.js.map