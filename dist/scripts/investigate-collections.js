import mongoose from "mongoose";
import dotenv from "dotenv";
// Load environment variables
dotenv.config();
const investigateCollections = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log("Connected to MongoDB");
        // Get database instance
        const db = mongoose.connection.db;
        // List all collections
        const collections = await db.listCollections().toArray();
        console.log("\n=== ALL COLLECTIONS IN DATABASE ===");
        console.log(`Total collections: ${collections.length}`);
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            console.log(`- ${collection.name}: ${count} documents`);
        }
        // Focus on user-related collections
        const userCollections = collections.filter(c => c.name.toLowerCase().includes('user') ||
            c.name === 'user' ||
            c.name === 'users');
        console.log("\n=== USER-RELATED COLLECTIONS ===");
        if (userCollections.length === 0) {
            console.log("No user-related collections found!");
        }
        else {
            for (const collection of userCollections) {
                const count = await db.collection(collection.name).countDocuments();
                console.log(`\n📁 Collection: ${collection.name}`);
                console.log(`   Documents: ${count}`);
                if (count > 0) {
                    // Show sample documents
                    const samples = await db.collection(collection.name).find({}).limit(3).toArray();
                    console.log(`   Sample documents:`);
                    for (let i = 0; i < samples.length; i++) {
                        console.log(`   ${i + 1}. ${JSON.stringify(samples[i], null, 2).substring(0, 200)}...`);
                    }
                }
                else {
                    console.log(`   ⚠️  Collection is empty`);
                }
            }
        }
        // Check for Better Auth collections
        const authCollections = collections.filter(c => c.name.toLowerCase().includes('auth') ||
            c.name.toLowerCase().includes('session') ||
            c.name.toLowerCase().includes('account'));
        console.log("\n=== BETTER AUTH COLLECTIONS ===");
        if (authCollections.length === 0) {
            console.log("No Better Auth collections found!");
        }
        else {
            for (const collection of authCollections) {
                const count = await db.collection(collection.name).countDocuments();
                console.log(`\n📁 Collection: ${collection.name}`);
                console.log(`   Documents: ${count}`);
                if (count > 0 && count <= 5) {
                    // Show sample documents for small collections
                    const samples = await db.collection(collection.name).find({}).limit(2).toArray();
                    console.log(`   Sample documents:`);
                    for (let i = 0; i < samples.length; i++) {
                        console.log(`   ${i + 1}. ${JSON.stringify(samples[i], null, 2).substring(0, 300)}...`);
                    }
                }
            }
        }
        // Check playlist collection for user references
        const playlistCollection = collections.find(c => c.name === 'playlists');
        if (playlistCollection) {
            console.log("\n=== PLAYLIST USER REFERENCES ===");
            const playlistCount = await db.collection('playlists').countDocuments();
            console.log(`Total playlists: ${playlistCount}`);
            if (playlistCount > 0) {
                const playlists = await db.collection('playlists').find({}).toArray();
                console.log("Playlist userId references:");
                for (const playlist of playlists) {
                    console.log(`- "${playlist.name}": userId = ${playlist.userId} (type: ${typeof playlist.userId})`);
                }
            }
        }
    }
    catch (error) {
        console.error("Error investigating collections:", error);
    }
    finally {
        mongoose.connection.close();
    }
};
// Run the investigation
investigateCollections();
//# sourceMappingURL=investigate-collections.js.map