{"version": 3, "file": "cleanup-collections.js", "sourceRoot": "", "sources": ["../../src/scripts/cleanup-collections.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,6BAA6B;AAC7B,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,kBAAkB,GAAG,KAAK,IAAmB,EAAE;IACpD,IAAI,CAAC;QACJ,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAY,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,wBAAwB;QACxB,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAElC,kDAAkD;QAClD,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QAE1E,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YACzE,OAAO;QACR,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,oCAAoC,UAAU,YAAY,CAAC,CAAC;QAExE,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;YAE1E,wBAAwB;YACxB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACzE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YACD,OAAO;QACR,CAAC;QAED,+DAA+D;QAC/D,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7E,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YACzE,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YACzE,OAAO;QACR,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,6CAA6C,SAAS,YAAY,CAAC,CAAC;QAEhF,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,uFAAuF,CAAC,CAAC;YACrG,OAAO;QACR,CAAC;QAED,4CAA4C;QAC5C,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,qBAAqB;QACrB,MAAM,oBAAoB,GAAG,MAAM,EAAE,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,CAAC;QAClE,MAAM,sBAAsB,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC9D,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CACrC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,KAAK,MAAM,UAAU,IAAI,sBAAsB,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,QAAQ,UAAU,CAAC,IAAI,KAAK,KAAK,YAAY,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAE7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QAChF,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;IACF,CAAC;YAAS,CAAC;QACV,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC,CAAC;AAEF,kBAAkB;AAClB,kBAAkB,EAAE,CAAC"}