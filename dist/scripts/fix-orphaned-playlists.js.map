{"version": 3, "file": "fix-orphaned-playlists.js", "sourceRoot": "", "sources": ["../../src/scripts/fix-orphaned-playlists.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC,MAAM,EAAE,CAAC;AAaT,MAAM,qBAAqB,GAAG,KAAK,IAAmB,EAAE;IACvD,IAAI,CAAC;QACJ,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAY,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,sCAAsC;QACtC,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC;YAC7C,GAAG,EAAE;gBACJ,EAAE,MAAM,EAAE,IAAI,EAAE;gBAChB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;aAC9B;SACD,CAAC,CAAC,MAAM,CAAC,4DAA4D,CAAC,CAAC;QAExE,OAAO,CAAC,GAAG,CAAC,WAAW,iBAAiB,CAAC,MAAM,sBAAsB,CAAC,CAAC;QACvE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,4DAA4D;QAC5D,MAAM,yBAAyB,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC;YAC1D;gBACC,OAAO,EAAE;oBACR,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,QAAQ;oBACpB,YAAY,EAAE,KAAK;oBACnB,EAAE,EAAE,UAAU;iBACd;aACD;YACD;gBACC,MAAM,EAAE;oBACP,IAAI,EAAE;wBACL,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;wBACzB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;wBAC7B,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;qBAC1B;iBACD;aACD;YACD;gBACC,QAAQ,EAAE;oBACT,IAAI,EAAE,CAAC;oBACP,WAAW,EAAE,CAAC;oBACd,QAAQ,EAAE,CAAC;oBACX,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,CAAC;oBACZ,SAAS,EAAE,CAAC;iBACZ;aACD;SACD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,WAAW,yBAAyB,CAAC,MAAM,0CAA0C,CAAC,CAAC;QACnG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7B,wCAAwC;QACxC,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC;MACV,QAAQ,CAAC,GAAG;SACT,QAAQ,CAAC,IAAI;gBACN,QAAQ,CAAC,WAAW,IAAI,KAAK;aAChC,QAAQ,CAAC,QAAQ;WACnB,QAAQ,CAAC,MAAM;eACX,QAAQ,CAAC,KAAK,CAAC,MAAM;WACzB,QAAQ,CAAC,SAAS;WAClB,QAAQ,CAAC,SAAS;EAC3B,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjB,CAAC;QACF,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAChD,CAAC;QAED,iDAAiD;QACjD,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,KAAK,MAAM,QAAQ,IAAI,yBAAyB,EAAE,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC;MACV,QAAQ,CAAC,GAAG;SACT,QAAQ,CAAC,IAAI;gBACN,QAAQ,CAAC,WAAW,IAAI,KAAK;aAChC,QAAQ,CAAC,QAAQ;WACnB,QAAQ,CAAC,MAAM;eACX,QAAQ,CAAC,KAAK,CAAC,MAAM;WACzB,QAAQ,CAAC,SAAS;WAClB,QAAQ,CAAC,SAAS;EAC3B,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjB,CAAC;QACF,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QACjE,CAAC;QAED,+CAA+C;QAC/C,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QACvD,MAAM,yBAAyB,GAAG,iBAAiB,CAAC,MAAM,GAAG,yBAAyB,CAAC,MAAM,CAAC;QAE9F,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,oBAAoB,cAAc,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,0BAA0B,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,qCAAqC,yBAAyB,CAAC,MAAM,EAAE,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,gCAAgC,yBAAyB,EAAE,CAAC,CAAC;QACzE,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,yBAAyB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5G,CAAC;QAED,2FAA2F;QAC3F,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,EAAE,CAAC,CAAC;QAErD,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,uBAAuB,SAAS,EAAE,QAAQ,KAAK,SAAS,EAAE,GAAG,GAAG,CAAC,CAAC;QAC/E,CAAC;IAEF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;YAAS,CAAC;QACV,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,KAAK,IAAmB,EAAE;IACzD,IAAI,CAAC;QACJ,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAY,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,wCAAwC;QACxC,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC;YAC7C,GAAG,EAAE;gBACJ,EAAE,MAAM,EAAE,IAAI,EAAE;gBAChB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;aAC9B;SACD,CAAC,CAAC;QAEH,8CAA8C;QAC9C,MAAM,yBAAyB,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC;YAC1D;gBACC,OAAO,EAAE;oBACR,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,QAAQ;oBACpB,YAAY,EAAE,KAAK;oBACnB,EAAE,EAAE,UAAU;iBACd;aACD;YACD;gBACC,MAAM,EAAE;oBACP,IAAI,EAAE;wBACL,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;wBACzB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;wBAC7B,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;qBAC1B;iBACD;aACD;SACD,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,GAAG,yBAAyB,CAAC,MAAM,CAAC;QAErF,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO;QACR,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,gBAAgB,mCAAmC,CAAC,CAAC;QAE1E,0BAA0B;QAC1B,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,4BAA4B,iBAAiB,CAAC,MAAM,IAAI,CAAC,CAAC;YACtE,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;QAED,8CAA8C;QAC9C,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,uCAAuC,yBAAyB,CAAC,MAAM,IAAI,CAAC,CAAC;YACzF,KAAK,MAAM,QAAQ,IAAI,yBAAyB,EAAE,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC,GAAG,eAAe,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YACpF,CAAC;QACF,CAAC;QAED,0CAA0C;QAC1C,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC;YAChD,GAAG,EAAE;gBACJ,EAAE,MAAM,EAAE,IAAI,EAAE;gBAChB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;aAC9B;SACD,CAAC,CAAC;QAEH,gDAAgD;QAChD,MAAM,cAAc,GAAG,yBAAyB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC;YAC/C,GAAG,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE;SAC5B,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,sCAAsC,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,4BAA4B,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC;QACvE,OAAO,CAAC,GAAG,CAAC,uCAAuC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;IAElF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;YAAS,CAAC;QACV,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC,CAAC;AAEF,MAAM,6BAA6B,GAAG,KAAK,EAAE,YAAoB,EAAiB,EAAE;IACnF,IAAI,CAAC;QACJ,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAY,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,gCAAgC;QAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,YAAY,aAAa,CAAC,CAAC;YACzD,OAAO;QACR,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;QAEvE,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC;YAC7C,GAAG,EAAE;gBACJ,EAAE,MAAM,EAAE,IAAI,EAAE;gBAChB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;aAC9B;SACD,CAAC,CAAC;QAEH,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO;QACR,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,iBAAiB,CAAC,MAAM,gCAAgC,CAAC,CAAC;QAC/E,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;QACvD,CAAC;QAED,8DAA8D;QAC9D,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,CACvC;YACC,GAAG,EAAE;gBACJ,EAAE,MAAM,EAAE,IAAI,EAAE;gBAChB,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;aAC9B;SACD,EACD;YACC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;SAC3D,CACD,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,aAAa,+BAA+B,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC;IAEtG,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;YAAS,CAAC;QACV,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC,CAAC;AAEF,iBAAiB;AACjB,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE;IACvB,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE/B,QAAQ,MAAM,EAAE,CAAC;QAChB,KAAK,MAAM;YACV,MAAM,qBAAqB,EAAE,CAAC;YAC9B,MAAM;QACP,KAAK,QAAQ;YACZ,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;YAC7E,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;YACxE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YACxD,MAAM,uBAAuB,EAAE,CAAC;YAChC,MAAM;QACP,KAAK,QAAQ;YACZ,IAAI,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBAC3E,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC;YACD,MAAM,6BAA6B,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM;QACP;YACC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;YACzF,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC;YAC1F,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;YAClG,MAAM;IACR,CAAC;AACF,CAAC,CAAC;AAEF,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}