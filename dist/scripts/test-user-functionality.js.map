{"version": 3, "file": "test-user-functionality.js", "sourceRoot": "", "sources": ["../../src/scripts/test-user-functionality.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,IAAI,EAAE,MAAM,yBAAyB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AAEvD,6BAA6B;AAC7B,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,qBAAqB,GAAG,KAAK,IAAmB,EAAE;IACvD,IAAI,CAAC;QACJ,MAAM,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAY,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAEpC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,oDAAoD;QACpD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,mCAAmC,cAAc,GAAG,CAAC,CAAC;QAElE,IAAI,cAAc,KAAK,MAAM,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,yBAAyB,cAAc,qBAAqB,CAAC,CAAC;QAC3E,CAAC;QAED,gCAAgC;QAChC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;YACnF,OAAO;QACR,CAAC;QAED,yDAAyD;QACzD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG;YAChB,GAAG,EAAE,UAAU;YACf,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,kBAAkB;YACzB,KAAK,EAAE,qBAAqB;YAC5B,aAAa,EAAE,KAAK;SACpB,CAAC;QAEF,IAAI,CAAC;YACJ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,wBAAwB,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;YAE/E,qBAAqB;YACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,sDAAsD;QACtD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,SAAS,EAAE,CAAC;YACf,MAAM,YAAY,GAAG;gBACpB,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,4BAA4B;gBACzC,MAAM,EAAE,SAAS,CAAC,GAAG;gBACrB,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE,EAAE;aACjB,CAAC;YAEF,IAAI,CAAC;gBACJ,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC5D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,4BAA4B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;gBAChE,OAAO,CAAC,GAAG,CAAC,mBAAmB,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;gBAEzD,2BAA2B;gBAC3B,MAAM,iBAAiB,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC1F,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;oBACnD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;oBACxD,OAAO,CAAC,GAAG,CAAC,0BAA2B,iBAAiB,CAAC,MAAc,CAAC,IAAI,EAAE,CAAC,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACP,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;gBACrD,CAAC;gBAED,yBAAyB;gBACzB,MAAM,QAAQ,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBACtD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC/C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;QACF,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC/D,CAAC;QAED,wCAAwC;QACxC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC7C,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAElC,yCAAyC;QACzC,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QAC1E,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACzD,CAAC;QAED,6CAA6C;QAC7C,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QACtE,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QAE1E,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,oBAAoB,WAAW,CAAC,MAAM,YAAY,CAAC,CAAC;QAEhE,mCAAmC;QACnC,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,UAAU,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACP,iBAAiB,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,+CAA+C,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAC3E,CAAC;QACF,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,UAAU,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACP,iBAAiB,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,iDAAiD,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/E,CAAC;QACF,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,eAAe,EAAE,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,+BAA+B,iBAAiB,EAAE,CAAC,CAAC;QAEhE,IAAI,iBAAiB,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;QACvE,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAEnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;YAAS,CAAC;QACV,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC,CAAC;AAEF,gBAAgB;AAChB,qBAAqB,EAAE,CAAC"}